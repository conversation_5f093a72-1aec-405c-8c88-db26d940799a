<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuth } from '@composables/useAuth'
import { useWebhookApi } from '@composables/useApi'
import { useToast } from '@composables/useToast'

interface Props {
  webhookData: {
    webhookId: string
    webhookUrl: string
    webhookName: string
  }
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
}>()

// Composables
const { user } = useAuth()
const { testWebhookCustom } = useWebhookApi()
const { success, error } = useToast()

// State
const isLoading = ref(false)
const testResult = ref<any>(null)

// Form data
const formData = ref({
  subject: 'Test Email from EmailConnect',
  textContent: 'This is a test email sent from EmailConnect to verify your webhook is working correctly.\n\nIf you receive this, your webhook integration is functioning properly!',
  htmlContent: '<p>This is a <strong>test email</strong> sent from EmailConnect to verify your webhook is working correctly.</p><p>If you receive this, your webhook integration is functioning properly!</p>'
})

// Plan-based restrictions
const isPro = computed(() => {
  return user.value?.planType === 'pro' || user.value?.planType === 'enterprise'
})

const isFieldDisabled = computed(() => !isPro.value)

// Methods
const handleTest = async () => {
  if (isLoading.value) return

  try {
    isLoading.value = true
    testResult.value = null

    const customPayload = isPro.value ? {
      subject: formData.value.subject,
      content: {
        text: formData.value.textContent,
        html: formData.value.htmlContent
      }
    } : undefined

    const result = await testWebhookCustom(props.webhookData.webhookId, customPayload)
    
    testResult.value = result
    success(`Test webhook sent successfully to ${props.webhookData.webhookName}!`)
    
  } catch (err) {
    console.error('Failed to test webhook:', err)
    error(err instanceof Error ? err.message : 'Failed to send test webhook')
  } finally {
    isLoading.value = false
  }
}

const resetForm = () => {
  formData.value = {
    subject: 'Test Email from EmailConnect',
    textContent: 'This is a test email sent from EmailConnect to verify your webhook is working correctly.\n\nIf you receive this, your webhook integration is functioning properly!',
    htmlContent: '<p>This is a <strong>test email</strong> sent from EmailConnect to verify your webhook is working correctly.</p><p>If you receive this, your webhook integration is functioning properly!</p>'
  }
  testResult.value = null
}

onMounted(() => {
  resetForm()
})
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div>
      <h3 class="text-lg font-medium text-base-content">Test Webhook</h3>
      <p class="text-sm text-base-content/70 mt-1">
        Send a test payload to your webhook endpoint
      </p>
    </div>

    <!-- Webhook URL (readonly) -->
    <div class="form-control">
      <label class="label">
        <span class="label-text">Webhook URL</span>
      </label>
      <input
        type="text"
        :value="webhookData.webhookUrl"
        class="input input-bordered bg-base-200"
        readonly
      />
    </div>

    <!-- Plan restriction notice for FREE users -->
    <div v-if="!isPro" class="alert alert-info">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <div>
        <h4 class="font-medium">Custom Test Content - PRO Feature</h4>
        <p class="text-sm">Upgrade to PRO to customize the test subject and content. FREE users can test with default content.</p>
      </div>
    </div>

    <!-- Subject Line -->
    <div class="form-control">
      <label class="label">
        <span class="label-text">Subject Line</span>
        <span v-if="!isPro" class="label-text-alt badge badge-warning badge-xs">PRO</span>
      </label>
      <input
        v-model="formData.subject"
        type="text"
        class="input input-bordered"
        :class="{ 'input-disabled': isFieldDisabled }"
        :disabled="isFieldDisabled"
        placeholder="Test Email from EmailConnect"
      />
    </div>

    <!-- Content Section -->
    <div class="space-y-4">
      <div class="flex items-center gap-2">
        <h4 class="font-medium text-base-content">Email Content</h4>
        <span v-if="!isPro" class="badge badge-warning badge-xs">PRO</span>
      </div>

      <!-- Text Content -->
      <div class="form-control">
        <label class="label">
          <span class="label-text">Text Content</span>
        </label>
        <textarea
          v-model="formData.textContent"
          class="textarea textarea-bordered h-24"
          :class="{ 'textarea-disabled': isFieldDisabled }"
          :disabled="isFieldDisabled"
          placeholder="Plain text version of the email..."
        ></textarea>
      </div>

      <!-- HTML Content -->
      <div class="form-control">
        <label class="label">
          <span class="label-text">HTML Content</span>
        </label>
        <textarea
          v-model="formData.htmlContent"
          class="textarea textarea-bordered h-32 font-mono text-sm"
          :class="{ 'textarea-disabled': isFieldDisabled }"
          :disabled="isFieldDisabled"
          placeholder="<p>HTML version of the email...</p>"
        ></textarea>
      </div>
    </div>

    <!-- Test Result -->
    <div v-if="testResult" class="alert alert-success">
      <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <div>
        <h4 class="font-medium">Test Sent Successfully!</h4>
        <div class="text-sm space-y-1">
          <p><strong>Message ID:</strong> {{ testResult.test?.messageId }}</p>
          <p><strong>Job ID:</strong> {{ testResult.test?.jobId }}</p>
          <p><strong>Sent At:</strong> {{ new Date(testResult.test?.sentAt).toLocaleString() }}</p>
        </div>
      </div>
    </div>

    <!-- Modal Actions -->
    <div class="modal-action">
      <button
        type="button"
        @click="resetForm"
        class="btn btn-ghost"
        :disabled="isLoading"
      >
        Reset
      </button>
      <button
        type="button"
        @click="emit('close')"
        class="btn btn-ghost"
        :disabled="isLoading"
      >
        Close
      </button>
      <button
        type="button"
        @click="handleTest"
        class="btn btn-primary"
        :disabled="isLoading"
      >
        <span v-if="isLoading" class="loading loading-spinner loading-sm"></span>
        {{ isLoading ? 'Sending...' : 'Send Test' }}
      </button>
    </div>
  </div>
</template>
